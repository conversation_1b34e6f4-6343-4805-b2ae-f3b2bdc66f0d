using MediatR;
using System;
using System.Threading;
using System.Threading.Tasks;
using WHO.MALARIA.Database;
using WHO.MALARIA.Domain.Commands;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Exceptions;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Domain.Models.Identity;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;
using WHO.MALARIA.Common.Services;
using WHO.MALARIA.Services.Rules.Shared;
using WHO.MALARIA.Services.Rules.User;

namespace WHO.MALARIA.Services.Handlers.Commands
{
    /// <summary>
    /// Handles user country access request rejection command
    /// </summary>
   public class UpdateCommentUserCountryAccessCommandHandler : RuleBase, IRequestHandler<UpdateCommentUserCountryAccessCommand, bool>
    {
        private readonly IMediator _mediator;
        private readonly IUnitOfWork _unitOfWork;
        private readonly ICommonRuleChecker _commonRuleChecker;
        private readonly IAssessmentRuleChecker _assessmentRuleChecker;
        private readonly ITranslationService _translationService;
        private readonly IUserRuleChecker _userRuleChecker;
        public UpdateCommentUserCountryAccessCommandHandler(IMediator mediator, IUnitOfWork unitOfWork, ICommonRuleChecker commonRuleChecker, IAssessmentRuleChecker assessmentRuleChecker, ITranslationService translationService, IUserRuleChecker userRuleChecker)
        {
            _mediator = mediator;
            _unitOfWork = unitOfWork;
            _commonRuleChecker = commonRuleChecker;
            _assessmentRuleChecker = assessmentRuleChecker;
            _translationService = translationService;
            _userRuleChecker = userRuleChecker;
        }

        /// <summary>
        /// Reject user's country access request using RejectUserCountryAccessCommand
        /// </summary>
        /// <param name="request"></param>
        /// <param name="cancellationToken"></param>
        /// <returns>It returns true if request status set as rejected successfully</returns>
        public async Task<bool> Handle(UpdateCommentUserCountryAccessCommand request, CancellationToken cancellationToken)
        {
            // Check Business Rules
            CheckRule(new GuidShouldNotBeEmptyRule(_translationService, _commonRuleChecker, request.UserCountryAccessId, "UserCountryAccessId"));

            // WHO admins can update comments on user country access requests for any country
            // Super managers can only update comments for their assigned country
            if (!_userRuleChecker.IsUserWHOAdmin(request.CurrentUserId))
            {
                CheckRule(new UserShouldBeSuperManagerOfCountry(_translationService, _userRuleChecker, request.CurrentUserId, request.CountryId));
            }

            UserCountryAccess userCountryAccess = _unitOfWork.UserCountryAccessRepository.GetAsync(u => u.Id == request.UserCountryAccessId).Result;

            if (userCountryAccess == null)
            {
                throw new RecordNotFoundException(request.UserCountryAccessId, "UserCountryAccess");
            }

            //Update user country access to reject
            userCountryAccess.Status = (int)UserCountryAccessRightsEnum.Pending;
            userCountryAccess.RejectionComment = request.RejectionComment;

            _unitOfWork.UserCountryAccessRepository.Update(userCountryAccess);

            if (await _unitOfWork.CommitAsync(cancellationToken) == 0)
            {
                throw new ApplicationException();
            }
            return true;
        }


    }
}
