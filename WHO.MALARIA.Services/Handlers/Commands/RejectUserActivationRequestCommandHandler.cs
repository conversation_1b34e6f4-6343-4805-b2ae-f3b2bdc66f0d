﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using WHO.MALARIA.Database;
using WHO.MALARIA.Domain.Commands;
using WHO.MALARIA.Domain.Events;
using WHO.MALARIA.Domain.Exceptions;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Domain.Models.Identity;
using WHO.MALARIA.Features;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;
using WHO.MALARIA.Common.Services;
using WHO.MALARIA.Services.Rules.User;

namespace WHO.MALARIA.Services.Handlers.Commands
{
    /// <summary>
    /// Command handler to handle rejection of user activation request using RejectUserActivationRequestCommand
    /// </summary>
    public class RejectUserActivationRequestCommandHandler : RuleBase, IRequestHandler<RejectUserActivationRequestCommand, bool>
    {
        private readonly IMediator _mediator;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IUserRuleChecker _userRuleChecker;
        private readonly IEmailService _emailService;
        private readonly ITranslationService _translationService;

        public RejectUserActivationRequestCommandHandler(
            IMediator mediator,
            IUnitOfWork unitOfWork,
            IUserRuleChecker userRuleChecker,
            IEmailService emailService,
            ITranslationService translationService)
        {
            _mediator = mediator;
            _unitOfWork = unitOfWork;
            _userRuleChecker = userRuleChecker;
            _emailService = emailService;
            _translationService = translationService;
        }

        /// <summary>
        /// Rejects user activation request using RejectUserActivationRequestCommand
        /// </summary>
        /// <param name="request">Command includes properties related to user activation request rejection</param>
        /// <param name="cancellationToken">Notify the cancellation request</param>
        /// <returns>True when request is rejected successfully; else false</returns>
        public async Task<bool> Handle(RejectUserActivationRequestCommand request, CancellationToken cancellationToken)
        { 
            User requesterUser = await _unitOfWork.UserRepository.Queryable(u => u.Id == request.UserId)
                                                              .Include(u => u.Identity)
                                                              .Include(u => u.UserCountryAccesses)
                                                              .FirstOrDefaultAsync();

            if (requesterUser == null)
            {
                throw new RecordNotFoundException(request.UserId, "User");
            }

            Country country = await _unitOfWork.CountryRepository.Queryable(u => u.Id == request.CountryId)
                                                 .FirstOrDefaultAsync();

            if (country == null)
            {
                throw new RecordNotFoundException(request.CountryId, "Country");
            }

            // Check Business Rules
            CheckRule(new UserStatusShouldBePendingRule(_translationService, _userRuleChecker, requesterUser.Identity.Email));

            // WHO admins can reject user activation requests for any country
            // Super managers can only reject for their assigned country
            if (!_userRuleChecker.IsUserWHOAdmin(request.CurrentUserId))
            {
                CheckRule(new UserShouldBeSuperManagerOfCountry(_translationService, _userRuleChecker, request.CurrentUserId, request.CountryId, country.Name));
            }

            string userEmail = requesterUser.Identity.Email;

            // Find the user's country access for the specific country
            UserCountryAccess userCountryAccess = requesterUser.UserCountryAccesses.FirstOrDefault(uca => uca.CountryId == country.Id);
            if (userCountryAccess == null)
            {
                throw new RecordNotFoundException(request.CountryId, $"UserCountryAccess for user {request.UserId} and country {country.Name}");
            }

            int userType = userCountryAccess.UserType;
           
            if (requesterUser.UserCountryAccesses.Count == 1)
            {
                // this means it is the last country for which the user activation request
                // is being rejected by the super manager, which means the user is completely rejected from the system
                // so totally delete the user from system

                // remove user country accesses
                _unitOfWork.UserCountryAccessRepository.Remove(userCountryAccess);

                // remove user
                _unitOfWork.UserRepository.Remove(requesterUser);

                // remove identity
                _unitOfWork.IdentityRepository.Remove(requesterUser.Identity);

            }
            else
            {
                // else just reject the country access request
                _unitOfWork.UserCountryAccessRepository.Remove(userCountryAccess);
            }

            if (await _unitOfWork.CommitAsync(cancellationToken) == 0)
            {
                throw new ApplicationException();
            }

            // send rejection email
            await _mediator.Publish(new UserActivationRequestRejectedEvent(userEmail, country.Name, userType, request.Reason), cancellationToken);

            return true;
        }
    }
}
