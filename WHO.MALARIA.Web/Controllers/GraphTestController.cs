using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;
using WHO.MALARIA.Services;
using Microsoft.AspNetCore.Http;
using WHO.MALARIA.Features.Helpers;
using WHO.MALARIA.Domain.Enum;
using System.Collections.Generic;
using Microsoft.Graph;
using Microsoft.Kiota.Abstractions;
using System.Net;
using WHO.MALARIA.Web.Apis;
using MediatR;
using System.Linq;
using WHO.MALARIA.Domain.Constants;
using System.Threading;

namespace WHO.MALARIA.Web.Controllers
{
    /// <summary>
    /// Test controller to verify Graph API permissions and diagnose invitation issues
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class GraphTestController : BaseApiController
    {
        private readonly ILogger<GraphTestController> _logger;
        private readonly IGraphService _graphService;

        public GraphTestController(
            ILogger<GraphTestController> logger,
            IGraphService graphService,
            IMediator mediator,
            IHttpContextAccessor httpContextAccessor) : base(mediator, httpContextAccessor)
        {
            _logger = logger;
            _graphService = graphService;
        }

        /// <summary>
        /// Test Graph API permissions and access token
        /// </summary>
        [HttpGet("test-permissions")]
        public async Task<IActionResult> TestGraphPermissions()
        {
            try
            {
                var currentUser = GetCurrentUser();
                if (!currentUser.IsAuthenticated)
                {
                    return BadRequest(new { error = "No current user found. Please login first." });
                }

                // Get email from claims
                var email = GetUserEmail();
                if (string.IsNullOrEmpty(email))
                {
                    return BadRequest(new { error = "No email found in user claims." });
                }

                // Use the new comprehensive test method
                var graphApiTestResult = await _graphService.TestGraphApiAccess(email);

                var result = new
                {
                    CurrentUser = new
                    {
                        UserId = currentUser.UserId,
                        Email = email,
                        Name = currentUser.Name,
                        UserType = currentUser.UserType,
                        IsAuthenticated = currentUser.IsAuthenticated
                    },
                    AccessTokenInfo = GetAccessTokenInfo(),
                    GraphApiTestResult = graphApiTestResult
                };

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error testing Graph API permissions");
                return StatusCode(500, new { error = ex.Message, stackTrace = ex.StackTrace });
            }
        }

        /// <summary>
        /// Test sending a B2B invitation to a specific email
        /// </summary>
        [HttpPost("test-invitation")]
        public async Task<IActionResult> TestInvitation([FromBody] TestInvitationRequest request)
        {
            try
            {
                var currentUser = GetCurrentUser();
                if (!currentUser.IsAuthenticated)
                {
                    return BadRequest(new { error = "No current user found. Please login first." });
                }

                // Get email from claims
                var currentUserEmail = GetUserEmail();
                if (string.IsNullOrEmpty(currentUserEmail))
                {
                    return BadRequest(new { error = "No email found in user claims." });
                }

                if (string.IsNullOrEmpty(request.Email))
                {
                    return BadRequest(new { error = "Email is required" });
                }

                _logger.LogInformation($"Testing invitation for email: {request.Email}");

                // Test the invitation
                var invitationResult = await _graphService.SendInvitation(
                    request.FirstName ?? "Test",
                    request.LastName ?? "User",
                    request.Email,
                    "This is a test invitation from the Malaria Surveillance Tool",
                    $"{HttpContext.Request.Scheme}://{HttpContext.Request.Host}/test-callback",
                    currentUserEmail,
                    "en"
                );

                var result = new
                {
                    InvitationStatus = invitationResult.Item1.ToString(),
                    RedeemUrl = invitationResult.Item2,
                    TestEmail = request.Email,
                    CurrentUser = currentUserEmail,
                    AccessTokenInfo = GetAccessTokenInfo()
                };

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error testing invitation for {request.Email}");
                return StatusCode(500, new {
                    error = ex.Message,
                    stackTrace = ex.StackTrace,
                    innerException = ex.InnerException?.Message
                });
            }
        }

        /// <summary>
        /// Test checking if a user exists in Azure AD
        /// </summary>
        [HttpPost("test-user-exists")]
        public async Task<IActionResult> TestUserExists([FromBody] TestUserExistsRequest request)
        {
            try
            {
                var currentUser = GetCurrentUser();
                if (!currentUser.IsAuthenticated)
                {
                    return BadRequest(new { error = "No current user found. Please login first." });
                }

                // Get email from claims
                var currentUserEmail = GetUserEmail();
                if (string.IsNullOrEmpty(currentUserEmail))
                {
                    return BadRequest(new { error = "No email found in user claims." });
                }

                if (string.IsNullOrEmpty(request.Email))
                {
                    return BadRequest(new { error = "Email is required" });
                }

                _logger.LogInformation($"Testing user existence for email: {request.Email}");

                var userExistsStatus = await _graphService.IsUserExistingInAzureAD(request.Email, currentUserEmail);

                var result = new
                {
                    UserExistsStatus = userExistsStatus.ToString(),
                    TestEmail = request.Email,
                    CurrentUser = currentUserEmail,
                    AccessTokenInfo = GetAccessTokenInfo()
                };

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error testing user existence for {request.Email}");
                return StatusCode(500, new {
                    error = ex.Message,
                    stackTrace = ex.StackTrace,
                    innerException = ex.InnerException?.Message
                });
            }
        }

        /// <summary>
        /// Test detailed restrictions by trying multiple email domains and scenarios
        /// </summary>
        [HttpGet("test-detailed-restrictions")]
        public async Task<IActionResult> TestDetailedRestrictions()
        {
            try
            {
                var currentUser = GetCurrentUser();
                if (!currentUser.IsAuthenticated)
                {
                    return BadRequest(new { error = "No current user found. Please login first." });
                }

                var currentUserEmail = GetUserEmail();
                if (string.IsNullOrEmpty(currentUserEmail))
                {
                    return BadRequest(new { error = "No email found in user claims." });
                }

                // Test multiple invitation scenarios
                var testResults = new List<object>();

                // Test 1: Try different email domains
                var testEmails = new[]
                {
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>"
                };

                foreach (var testEmail in testEmails)
                {
                    try
                    {
                        _logger.LogInformation($"Testing detailed invitation for: {testEmail}");

                        var result = await _graphService.SendInvitation(
                            "Test",
                            "User",
                            testEmail,
                            "Diagnostic test invitation",
                            $"{HttpContext.Request.Scheme}://{HttpContext.Request.Host}/test-callback",
                            currentUserEmail,
                            "en"
                        );

                        testResults.Add(new
                        {
                            testEmail,
                            status = result.Item1.ToString(),
                            success = result.Item1 == WHO.MALARIA.Domain.Enum.SendInvitationStatus.InvitedSuccesfully,
                            error = result.Item1 != WHO.MALARIA.Domain.Enum.SendInvitationStatus.InvitedSuccesfully ? result.Item1.ToString() : null,
                            redeemUrl = result.Item2
                        });
                    }
                    catch (Exception ex)
                    {
                        testResults.Add(new
                        {
                            testEmail,
                            status = "Exception",
                            success = false,
                            error = ex.Message,
                            redeemUrl = (string)null
                        });
                    }
                }

                return Ok(new
                {
                    currentUser = new
                    {
                        email = currentUserEmail,
                        name = currentUser.Name,
                        userType = currentUser.UserType.ToString()
                    },
                    testResults,
                    summary = new
                    {
                        totalTests = testResults.Count,
                        successfulTests = testResults.Count(r => (bool)r.GetType().GetProperty("success").GetValue(r)),
                        failedTests = testResults.Count(r => !(bool)r.GetType().GetProperty("success").GetValue(r))
                    },
                    accessTokenInfo = GetAccessTokenInfo()
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error testing detailed restrictions");
                return StatusCode(500, new
                {
                    error = "Failed to test detailed restrictions",
                    message = ex.Message,
                    stackTrace = ex.StackTrace
                });
            }
        }

        /// <summary>
        /// Test raw Graph API invitation to get detailed error information
        /// </summary>
        [HttpPost("test-raw-invitation")]
        public async Task<IActionResult> TestRawInvitation([FromBody] TestInvitationRequest request)
        {
            try
            {
                var currentUser = GetCurrentUser();
                if (!currentUser.IsAuthenticated)
                {
                    return BadRequest(new { error = "No current user found. Please login first." });
                }

                var currentUserEmail = GetUserEmail();
                if (string.IsNullOrEmpty(currentUserEmail))
                {
                    return BadRequest(new { error = "No email found in user claims." });
                }

                if (string.IsNullOrEmpty(request.Email))
                {
                    return BadRequest(new { error = "Email is required" });
                }

                // Get access token and create Graph client directly
                string accessToken = HttpContext.Request.Cookies["access_token"];
                if (string.IsNullOrEmpty(accessToken))
                {
                    return BadRequest(new { error = "No access token found" });
                }

                var tokenProvider = new Microsoft.Kiota.Abstractions.Authentication.BaseBearerTokenAuthenticationProvider(
                    new TokenProvider(accessToken));
                var graphClient = new Microsoft.Graph.GraphServiceClient(tokenProvider);

                try
                {
                    // Try to send invitation directly via Graph API
                    var invitation = new Microsoft.Graph.Models.Invitation
                    {
                        InviteRedirectUrl = $"{HttpContext.Request.Scheme}://{HttpContext.Request.Host}/test-callback",
                        InvitedUserDisplayName = $"{request.FirstName ?? "Test"} {request.LastName ?? "User"}",
                        InvitedUserEmailAddress = request.Email,
                        InvitedUserMessageInfo = new Microsoft.Graph.Models.InvitedUserMessageInfo
                        {
                            CustomizedMessageBody = "This is a raw Graph API test invitation",
                            MessageLanguage = "en"
                        },
                        SendInvitationMessage = false
                    };

                    var invitationResponse = await graphClient.Invitations.PostAsync(invitation);

                    return Ok(new
                    {
                        success = true,
                        message = "Raw Graph API invitation succeeded",
                        invitationId = invitationResponse?.Id,
                        redeemUrl = invitationResponse?.InviteRedeemUrl,
                        status = invitationResponse?.Status,
                        testEmail = request.Email
                    });
                }
                catch (Microsoft.Kiota.Abstractions.ApiException apiEx)
                {
                    return Ok(new
                    {
                        success = false,
                        message = "Raw Graph API invitation failed",
                        errorType = "ApiException",
                        statusCode = apiEx.ResponseStatusCode,
                        errorMessage = apiEx.Message,
                        errorDetails = apiEx.ToString(),
                        testEmail = request.Email
                    });
                }
                catch (Exception ex)
                {
                    return Ok(new
                    {
                        success = false,
                        message = "Raw Graph API invitation failed",
                        errorType = ex.GetType().Name,
                        errorMessage = ex.Message,
                        errorDetails = ex.ToString(),
                        testEmail = request.Email
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in TestRawInvitation");
                return StatusCode(500, new
                {
                    error = "Failed to test raw invitation",
                    message = ex.Message,
                    stackTrace = ex.StackTrace
                });
            }
        }

        private string GetUserEmail()
        {
            try
            {
                var emailClaim = HttpContext.User.Claims
                    .FirstOrDefault(c => c.Type.Equals(WHO.MALARIA.Domain.Constants.Constants.IdentityClaims.Email, StringComparison.InvariantCultureIgnoreCase));
                return emailClaim?.Value ?? string.Empty;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user email from claims");
                return string.Empty;
            }
        }

        private object GetAccessTokenInfo()
        {
            try
            {
                string accessToken = HttpContext.Request.Cookies["access_token"];

                if (string.IsNullOrEmpty(accessToken))
                {
                    return new { hasAccessToken = false, error = "No access token found in cookies" };
                }

                // Basic token info (don't expose the actual token for security)
                return new
                {
                    hasAccessToken = true,
                    tokenLength = accessToken.Length,
                    tokenPrefix = accessToken.Substring(0, Math.Min(10, accessToken.Length)) + "...",
                    cookieExists = true
                };
            }
            catch (Exception ex)
            {
                return new { hasAccessToken = false, error = ex.Message };
            }
        }


    }

    public class TestInvitationRequest
    {
        public string Email { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
    }

    public class TestUserExistsRequest
    {
        public string Email { get; set; }
    }

    public class TestUserActivationRequest
    {
        public Guid UserId { get; set; }
        public Guid UserCountryAccessId { get; set; }
        public string Email { get; set; }
    }

    /// <summary>
    /// Token provider for Graph API authentication
    /// </summary>
    public class TokenProvider : Microsoft.Kiota.Abstractions.Authentication.IAccessTokenProvider
    {
        private readonly string _accessToken;

        public TokenProvider(string accessToken)
        {
            _accessToken = accessToken;
        }

        public Task<string> GetAuthorizationTokenAsync(Uri uri, Dictionary<string, object> additionalAuthenticationContext = null, CancellationToken cancellationToken = default)
        {
            return Task.FromResult(_accessToken);
        }

        public Microsoft.Kiota.Abstractions.Authentication.AllowedHostsValidator AllowedHostsValidator { get; } = new Microsoft.Kiota.Abstractions.Authentication.AllowedHostsValidator();
    }
}
