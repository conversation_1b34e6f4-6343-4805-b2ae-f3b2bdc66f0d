import { ChangeEvent } from "react";
import { <PERSON><PERSON> } from "@mui/material";
import { useState, useEffect } from "react";
import classes from "../user.module.scss";
import { useTranslation } from "react-i18next";
import Modal from "../../controls/Modal";
import AddUser from "./AddUser";
import { GridCellProps, GridColumnProps } from "@progress/kendo-react-grid";
import classNames from "classnames";
import { UserListModel } from "../../../models/UserModel";
import { userService } from "../../../services/userService";
import {
  ChangeUserStatusRequestModel,
  ChangeUserTypeRequestModel,
  CountryAccessRequestModel,
  InvitationRequestModel,
  PendingRequestModel,
  PendingUserDetailModel,
  ResendInvitationRequestModel,
} from "../../../models/RequestModels/UserRequestModel";
import {
  DialogAction,
  UserCountryAccessRightsEnum,
  UserRoleEnum,
  UserStatus,
} from "../../../models/Enums";
import { Users as UserList } from "../Users";
import ConfirmationDialog from "../../common/ConfirmationDialog";
import Checkbox from "../../controls/Checkbox";
import { Constants } from "../../../models/Constants";

/** Renders the WHO Admin  and Super Manager user screen */
export const Users = () => {
  const { t } = useTranslation();
  document.title = t("app.UsersTitle");
  const [showAddUserModal, setShowAddUserModal] = useState<boolean>(false);
  const toggleAddUserModal = () =>
    setShowAddUserModal((prevState: boolean) => !prevState);
  const [hover, setHover] = useState<string>("");
  const [users, setUsers] = useState<Array<UserListModel>>([]);
  const [showConfirmationDialog, setShowConfirmationDialog] =
    useState<boolean>(false);
  const [registerUser, setRegisterUser] = useState<boolean>(false);

  const [viewerUser, setViewerUser] = useState<ChangeUserTypeRequestModel>(
    ChangeUserTypeRequestModel.init()
  );

  useEffect(() => {
    bindGrid();
  }, []);

  // Bind grid by calling api
  const bindGrid = () => {
    userService
      .getSuperManagersWHOUsersRequestedByWHOAdmin()
      .then((users: Array<UserListModel>) => {
        users?.map(user => {
          user.displayStatus = getUseStatusByUserType(
            user.userType,
            user.status
          );
        });
        setUsers(users);
      });
  };

  // Bind registered user grid
  const bindRegisteredUserGrid = () => {
    userService
      .getNewlyRegisteredInActiveViewers()
      .then((users: Array<UserListModel>) => {
        const usersData: Array<UserListModel> = users.map(
          (user: UserListModel) => {
            user.displayStatus = getUserCountryAccessStatus(user.status);
            return user;
          }
        );
        setUsers(usersData);
      });
  };

  // get user status by enum
  const getUserStatus = (userStatus: number) => {
    switch (userStatus) {
      case UserStatus.Active:
        return t("Common.Active");
      case UserStatus.InActive:
        return t("Common.Inactive");
      case UserStatus.InvitationSent:
        return t("Common.InvitationSent");
      case UserStatus.Pending:
      case UserCountryAccessRightsEnum.Pending:
        return t("Common.InvitationNotAccepted");
      default:
        return t("Common.NotApplicable");
    }
  };

  // get user country access status
  const getUserCountryAccessStatus = (userCountryAccessStatus: number) => {
    switch (userCountryAccessStatus) {
      case UserCountryAccessRightsEnum.Accepted:
        return t("Common.Active");
      case UserCountryAccessRightsEnum.InActive:
      case UserCountryAccessRightsEnum.Pending:
        return t("Common.Inactive");
      case UserCountryAccessRightsEnum.Rejected:
        return t("Common.Reject");
      case UserCountryAccessRightsEnum.InvitationNotAccepted:
        return t("Common.InvitationNotAccepted");
      default:
        return t("Common.NotApplicable");
    }
  };

  // get user status class name by enum
  const getClassNameByUserStatus = (userStatus: number) => {
    switch (userStatus) {
      case UserStatus.Active:
        return "btn-green";
      case UserStatus.InActive:
        return "btn-grey";
      case UserStatus.InvitationSent:
        return "btn-blue";
      case UserStatus.Pending:
        return "btn-orange";
      default:
        return "";
    }
  };

  // get user status class name by enum
  const getClassNameByUserCountryAccessStatus = (
    userCountryAccessStatus: number
  ) => {
    switch (userCountryAccessStatus) {
      case UserCountryAccessRightsEnum.Accepted:
        return "btn-green";
      case UserCountryAccessRightsEnum.InActive:
      case UserCountryAccessRightsEnum.Pending:
        return "btn-grey";
      case UserCountryAccessRightsEnum.InvitationNotAccepted:
        return "btn-orange";
      default:
        return "";
    }
  };

  // get class name based on user type and status
  const getClassNameByUserTypeAndStatus = (
    userType: number,
    status: number
  ) => {
    if (userType == UserRoleEnum.WHOAdmin) {
      return getClassNameByUserStatus(status);
    } else {
      return getClassNameByUserCountryAccessStatus(status);
    }
  };

  // get user status based on user type and status
  const getUseStatusByUserType = (userType: number, status: number) => {
    if (userType == UserRoleEnum.WHOAdmin) {
      return getUserStatus(status);
    } else {
      return getUserCountryAccessStatus(status);
    }
  };

  // get user action label for who admin
  const getUserActionWHOAdminLabel = (userType: number, status: number) => {
    if (userType == UserRoleEnum.Viewer) {
      return t("UserManagement.ChangeToSuperManager");
    } else if (status == UserStatus.Active) {
      return t("UserManagement.DeActivate");
    } else if (
      status == UserStatus.Pending ||
      status == UserCountryAccessRightsEnum.InvitationNotAccepted
    ) {
      return t("UserManagement.ResendInvitation");
    } else {
      return t("UserManagement.Activate");
    }
  };

  // get other user action label
  const getUserActionOtherLabel = (userType: number, status: number) => {
    if (userType == UserRoleEnum.Viewer) {
      return t("UserManagement.ChangeToSuperManager");
    } else if (status == UserCountryAccessRightsEnum.Accepted) {
      return t("UserManagement.DeActivate");
    } else if (status == UserCountryAccessRightsEnum.InvitationNotAccepted) {
      return t("UserManagement.ResendInvitation");
    } else {
      return t("UserManagement.Activate");
    }
  };

  //const [pendingRequest, setPendingRequest] =
  //    useState<PendingUserDetailModel | undefined>();
  const [pendingRequests, setPendingRequests] = useState<PendingRequestModel>(
    PendingRequestModel.init()
  );
  const [countryAccessRequest, setCountryAccessRequest] =
    useState<CountryAccessRequestModel>(CountryAccessRequestModel.init());
  let selectedCountryId =
    sessionStorage.getItem(Constants.SessionStorageKey.SELECTED_COUNTRY) || "";

  // retrieve the pending requests
  const getPendingRequests = () => {
    userService
      .getPendingRequests(selectedCountryId)
      .then((pendingRequests: PendingRequestModel) => {
        setPendingRequests(pendingRequests);
      });
  };

  // triggers whenever user clicks on reject
  const onUserReject = (id: string, countryId: string, comment: string) => {
    userService.onReject(id, countryId, comment).then((response: boolean) => {
      if (response) {
        getPendingRequests();
      }
    });
  };

  // triggers whenever user clicks on invite
  const onUserInvite = (
    id: string,
    userCountryAccessId: string,
    email: string
  ) => {
    userService
      .sendInvitation(
        new InvitationRequestModel(id, userCountryAccessId, email)
      )
      .then((response: boolean) => {
        if (response) {
          getPendingRequests();
        } else {
          // Handle case where response is false but no exception was thrown
          notificationService.sendMessage(
            new BaseMessageModel(
              t("UserManagement.Message.UserInvitationFailed"),
              StatusCode.InternalServerError
            )
          );
        }
      })
      .catch((error: any) => {
        // Handle invitation failure
        console.error("User invitation failed:", error);

        // Refresh the list to show current state
        getPendingRequests();

        // Show appropriate error message based on error type
        let errorMessage = t("Errors.SomethingWentWrong");

        if (error && error.statusCode === StatusCode.PreConditionFailed) {
          errorMessage =
            error.text ||
            t("UserManagement.Message.UserInvitationValidationFailed");
        } else if (error && error.statusCode === StatusCode.Forbidden) {
          errorMessage = t("UserManagement.Message.InsufficientPrivileges");
        } else if (error && error.text) {
          errorMessage = error.text;
        }

        notificationService.sendMessage(
          new BaseMessageModel(
            errorMessage,
            error?.statusCode || StatusCode.InternalServerError
          )
        );
      });
  };

  // get user type action button
  const getUserTypeAction = (
    id: string,
    countryId: string,
    userType: number,
    status: number
  ) => {
    if (userType == UserRoleEnum.WHOAdmin) {
      return (
        <Button
          className={
            hover && hover === id
              ? "app-btn-secondary"
              : "app-btn-secondary app-btn-secondary-grey"
          }
          onClick={() => changeUserStatus(id, countryId, status, userType)}
        >
          {getUserActionWHOAdminLabel(userType, status)}
        </Button>
      );
    } else {
      return (
        <Button
          className={
            hover && hover === id
              ? "app-btn-secondary"
              : "app-btn-secondary app-btn-secondary-grey"
          }
          onClick={() =>
            userType === UserRoleEnum.Viewer
              ? changeUserType(id, countryId)
              : changeUserStatus(id, countryId, status, userType)
          }
        >
          {getUserActionOtherLabel(userType, status)}
        </Button>
      );
    }
  };
  // get additional column for grid
  const additionalColumns: Array<GridColumnProps> = [
    {
      field: "displayStatus",
      title: t("UserManagement.GridColumn.Status"),
      sortable: true,
      filterable: false,
      width: 150,
      cells: {
        data: (props: GridCellProps) => (
          <td title={props.dataItem["displayStatus"]}>
            <span
              className={getClassNameByUserTypeAndStatus(
                props.dataItem["userType"],
                props.dataItem["status"]
              )}
            >
              {props.dataItem["displayStatus"]}
            </span>
          </td>
        ),
      },
    },
    {
      sortable: false,
      filterable: false,
      cells: {
        data: (props: GridCellProps) => (
          <td
            className={classNames(classes.customCell)}
            title={getUserActionWHOAdminLabel(
              props.dataItem["userType"],
              props.dataItem["status"]
            )}
          >
            {getUserTypeAction(
              props.dataItem["id"],
              props.dataItem["countryId"],
              props.dataItem["userType"],
              props.dataItem["status"]
            )}
          </td>
        ),
      },
    },
  ];

  // additional column for registered user grid
  const additionalRegisteredUserColumns: Array<GridColumnProps> = [
    {
      field: "comment",
      title: t("UserManagement.GridColumn.Comment"),
      sortable: true,
      filterable: false,
      width: 300,
      cells: {
        data: (props: GridCellProps) => (
          <td title={props.dataItem["comment"]}>
            <span>{props.dataItem["comment"]}</span>
          </td>
        ),
      },
    },
    {
      field: "status",
      title: t("UserManagement.GridColumn.Status"),
      sortable: true,
      filterable: false,
      width: 200,
      cells: {
        data: (props: GridCellProps) => (
          <td title={t("UserManagement.RegisteredNewUser")}>
            <span>{t("UserManagement.RegisteredNewUser")}</span>
          </td>
        ),
      },
    },
    {
      sortable: false,
      filterable: false,
      width: 500,
      cells: {
        data: (props: GridCellProps) => (
          <td className={classNames(classes.customCell)}>
            <Button
              className={
                hover && hover === props.dataItem["id"]
                  ? "app-btn-secondary"
                  : "app-btn-secondary app-btn-secondary-grey me-2"
              }
              onClick={() =>
                onUserInvite(
                  props.dataItem["id"],
                  props.dataItem["userCountryAccessId"],
                  props.dataItem["email"]
                )
              }
            >
              {t("Common.Invite")}
            </Button>

            <Button
              className={
                hover && hover === props.dataItem["id"]
                  ? "app-btn-secondary"
                  : "app-btn-secondary app-btn-secondary-grey me-2"
              }
              onClick={() =>
                onUserReject(
                  props.dataItem["id"],
                  props.dataItem["countryId"],
                  props.dataItem["comment"]
                )
              }
            >
              {t("Common.Reject")}
            </Button>
            <Button
              className={
                hover && hover === props.dataItem["id"]
                  ? "app-btn-secondary"
                  : "app-btn-secondary app-btn-secondary-grey"
              }
              title={t("UserManagement.ChangeToSuperManager")}
              onClick={() =>
                changeUserType(
                  props.dataItem["id"],
                  props.dataItem["countryId"]
                )
              }
            >
              {t("UserManagement.ChangeToSuperManager")}
            </Button>
          </td>
        ),
      },
    },
  ];

  // triggers whenever user perform action on dialog
  const onDialogClose = (action: DialogAction) => {
    if (action === DialogAction.Add) {
      bindGridBasedOnRegisteredUserCheckbox(registerUser);
    }
    setShowAddUserModal(false);
  };

  //Handles confirmation dialog box actions
  const onConfirmationButtonClick = (action: DialogAction) => {
    switch (action) {
      case DialogAction.Add:
        userService.makeViewerAsSupermanager(viewerUser).then(response => {
          if (response) {
            bindGridBasedOnRegisteredUserCheckbox(registerUser);
          }
        });
        setShowConfirmationDialog(false);
        break;
      case DialogAction.Close:
        setShowConfirmationDialog(false);
        break;
    }
  };

  // triggered when user clicks on Active or InActive or Resend Invitation Email
  const changeUserStatus = (
    id: string,
    countryId: string,
    status: number,
    userType: number
  ) => {
    if (userType == UserRoleEnum.WHOAdmin) {
      if (status !== UserStatus.Pending) {
        const userStatus = status === UserStatus.Active ? 0 : 1;
        const superManagerUser = new ChangeUserStatusRequestModel(
          id,
          countryId,
          Boolean(userStatus),
          userType
        );
        userService.changeUserStatus(superManagerUser).then(response => {
          if (response) {
            bindGridBasedOnRegisteredUserCheckbox(registerUser);
          }
        });
      } else if (status === UserStatus.Pending) {
        const superManagerUser = new ResendInvitationRequestModel(
          id,
          countryId
        );
        userService.resendInvitation(superManagerUser);
      }
    } else {
      if (status != UserCountryAccessRightsEnum.InvitationNotAccepted) {
        const userStatus = status === UserStatus.Active ? 0 : 1;
        const superManagerUser = new ChangeUserStatusRequestModel(
          id,
          countryId,
          Boolean(userStatus),
          userType
        );
        userService.changeUserStatus(superManagerUser).then(response => {
          if (response) {
            bindGridBasedOnRegisteredUserCheckbox(registerUser);
          }
        });
      } else if (status == UserCountryAccessRightsEnum.InvitationNotAccepted) {
        const superManagerUser = new ResendInvitationRequestModel(
          id,
          countryId
        );
        userService.resendInvitation(superManagerUser);
      }
    }
  };

  // triggered when user clicks on Change to SuperManager
  const changeUserType = (id: string, countryId: string) => {
    setShowConfirmationDialog(true);

    setViewerUser(new ChangeUserTypeRequestModel(id, countryId));
  };

  //Triggers onChange of RegisterNewUser checkbox
  const onRegisterNewUserChange = (evt: ChangeEvent<HTMLInputElement>) => {
    setRegisterUser(evt.currentTarget.checked);
    bindGridBasedOnRegisteredUserCheckbox(evt.currentTarget.checked);
  };

  // Grid should be bound based on registered user checkbox check
  const bindGridBasedOnRegisteredUserCheckbox = (registerUser: boolean) => {
    if (registerUser) {
      bindRegisteredUserGrid();
    } else {
      bindGrid();
    }
  };

  return (
    <section className='page-full-section page-grid-section'>
      <div className='container-fluid'>
        <div className='d-flex document-title-section'>
          <h2 className='heading-title'>{t("UserManagement.AllUsers")}</h2>
          <div className='ml-auto'>
            <Checkbox
              id='registerUser'
              name='registerUser'
              label={t("UserManagement.RegisteredNewUser")}
              onChange={onRegisterNewUserChange}
              checked={registerUser}
            />
          </div>
          <div className='button-action-section ml-auto'>
            <Button
              className='btn app-btn-primary ripple'
              onClick={toggleAddUserModal}
            >
              {t("UserManagement.AddNewUser")}
            </Button>
          </div>
        </div>

        <div className={classNames(classes.dataWrapper)}>
          {registerUser ? (
            <UserList
              users={users}
              additionalColumns={additionalRegisteredUserColumns}
            />
          ) : (
            <UserList users={users} additionalColumns={additionalColumns} />
          )}
        </div>
      </div>
      <Modal
        open={showAddUserModal}
        title={t("UserManagement.AddNewUser")}
        onEscPress={true}
        onDialogClose={toggleAddUserModal}
        modalClassName='app-modal-md'
      >
        <AddUser onDialogClose={onDialogClose} />
      </Modal>
      <ConfirmationDialog
        title={t("Common.ConfirmationTitle")}
        content={t("Common.ConfirmationViewerToSuperManager")}
        open={showConfirmationDialog}
        onClick={onConfirmationButtonClick}
      />
    </section>
  );
};
